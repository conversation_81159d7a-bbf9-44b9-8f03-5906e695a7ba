<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreServerRequest;
use App\Http\Requests\UpdateServerRequest;
use App\Http\Services\ServerService;
use App\Models\Server;
use App\Models\GatewayProviderType;
use Illuminate\Http\Request;

class ServerController extends Controller
{
    private $server;

    public function __construct(ServerService $server)
    {
        $this->server = $server;
        $this->middleware(['permission:server-list|server-create|server-edit|server-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:server-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:server-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:server-delete'], ['only' => ['destroy']]);
    }

    public function browse(Request $request)
    {
        return datatables()->of(
            Server::with('gatewayProviderType')->query()
        )
            ->addColumn('provider_name', function (Server $server) {
                if ($server->is_legacy) {
                    // For legacy servers, show the name as provider
                    return $server->name;
                } else {
                    // For dynamic servers, show the gateway provider type display name
                    return $server->gatewayProviderType ? $server->gatewayProviderType->display_name : 'Unknown';
                }
            })
            ->addColumn('action', function (Server $server) {
                $action = ' <a href="'.route('admin.servers.edit', $server->id).'" class="btn btn-secondary btn-sm">Edit</a>';
                $action .= ' <a href="'.route('admin.servers.show', $server->id).'" class="btn btn-success btn-sm">View</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('admin.servers.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $smsProviders = Common::smsProviders();
        $gatewayProviderTypes = GatewayProviderType::where('is_active', true)
            ->orderBy('is_built_in', 'desc')
            ->orderBy('display_name')
            ->get();

        return view('admin.servers.create', [
            'smsProviders' => $smsProviders,
            'gatewayProviderTypes' => $gatewayProviderTypes,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreServerRequest $request)
    {
        $this->server->store($request);

        return redirect()->route('admin.servers.index')
            ->with('success', 'Server created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Server $server)
    {
        return view('admin.servers.show', compact('server'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Server $server)
    {
        $smsProviders = Common::smsProviders();
        $gatewayProviderTypes = GatewayProviderType::where('is_active', true)
            ->orderBy('is_built_in', 'desc')
            ->orderBy('display_name')
            ->get();

        return view('admin.servers.edit', [
            'server' => $server,
            'smsProviders' => $smsProviders,
            'gatewayProviderTypes' => $gatewayProviderTypes,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateServerRequest $request, Server $server)
    {
        $this->server->update($request, $server);

        return redirect()->route('admin.servers.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Server $server)
    {
        $this->server->destroy($server);

        return redirect()->route('admin.servers.index');
    }

    /**
     * Get gateway provider type configuration via AJAX
     */
    public function getGatewayConfig(Request $request)
    {
        $gatewayType = $request->get('gateway_type');

        if (!$gatewayType) {
            return response()->json(['error' => 'Gateway type is required'], 400);
        }

        $providerType = GatewayProviderType::where('name', $gatewayType)
            ->where('is_active', true)
            ->first();

        if (!$providerType) {
            return response()->json(['error' => 'Gateway provider type not found'], 404);
        }

        return response()->json([
            'provider_type' => $providerType,
            'auth_methods' => GatewayProviderType::getAuthMethods(),
            'http_methods' => GatewayProviderType::getHttpMethods(),
            'message_types' => GatewayProviderType::getMessageTypes(),
            'parameter_placeholders' => GatewayProviderType::getParameterPlaceholders()
        ]);
    }

    /**
     * Test server configuration
     */
    public function testConfiguration(Request $request, Server $server)
    {
        try {
            // This would implement a test SMS sending functionality
            // For now, just validate the configuration
            $errors = [];

            if ($server->gatewayProviderType) {
                $errors = $server->gatewayProviderType->validateConfiguration([
                    'api_key' => $server->api_key,
                    'username' => $server->username,
                    'password' => $server->password,
                    'api_link' => $server->api_link
                ]);
            }

            if (!empty($errors)) {
                return response()->json(['success' => false, 'errors' => $errors], 400);
            }

            return response()->json(['success' => true, 'message' => 'Configuration is valid']);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
